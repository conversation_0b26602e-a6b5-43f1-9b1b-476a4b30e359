<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="description" content="Dakoii Admin Portal - Agricultural Data Management System">
    <meta name="author" content="Dakoii Systems">
    <link rel="shortcut icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --dakoii-primary: #2c3e50;
            --dakoii-secondary: #34495e;
            --dakoii-accent: #3498db;
            --dakoii-success: #27ae60;
            --dakoii-warning: #f39c12;
            --dakoii-danger: #e74c3c;
            --dakoii-light: #ecf0f1;
            --dakoii-dark: #2c3e50;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--dakoii-primary) 0%, var(--dakoii-secondary) 100%);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header .logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }

        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            color: white !important;
            transform: translateX(5px);
        }

        .nav-link.active {
            background-color: var(--dakoii-accent);
            color: white !important;
            border-left: 4px solid #fff;
        }

        .nav-link i {
            width: 20px;
            margin-right: 10px;
            text-align: center;
        }

        .main-content {
            margin-left: 250px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-left: 70px;
        }

        .top-navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 2rem;
            margin-bottom: 2rem;
        }

        .content-wrapper {
            padding: 0 2rem 2rem 2rem;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--dakoii-primary) 0%, var(--dakoii-secondary) 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: var(--dakoii-accent);
            border-color: var(--dakoii-accent);
        }

        .btn-primary:hover {
            background: #2980b9;
            border-color: #2980b9;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background-color: var(--dakoii-light);
            border: none;
            font-weight: 600;
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--dakoii-primary);
            font-size: 1.2rem;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .main-content.expanded {
                margin-left: 0;
            }
        }

        .footer {
            background: var(--dakoii-primary);
            color: white;
            text-align: center;
            padding: 1rem;
            margin-top: 2rem;
        }
    </style>
    
    <title><?= $title ?? 'Dakoii Admin Portal' ?></title>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Dakoii Logo" class="logo">
            <h5 class="text-white mt-2 sidebar-title">Dakoii Admin</h5>
        </div>
        
        <ul class="nav flex-column sidebar-nav">
            <li class="nav-item">
                <a class="nav-link <?= ($menu ?? '') == 'dashboard' ? 'active' : '' ?>" href="<?= base_url('dakoii/dashboard') ?>">
                    <i class="fas fa-tachometer-alt"></i>
                    <span class="nav-text">Dashboard</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= ($menu ?? '') == 'organizations' ? 'active' : '' ?>" href="<?= base_url('dakoii/organizations') ?>">
                    <i class="fas fa-building"></i>
                    <span class="nav-text">Organizations</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= ($menu ?? '') == 'users' ? 'active' : '' ?>" href="<?= base_url('dakoii/users') ?>">
                    <i class="fas fa-users"></i>
                    <span class="nav-text">Dakoii Users</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= ($menu ?? '') == 'system_admins' ? 'active' : '' ?>" href="<?= base_url('dakoii/system-admins') ?>">
                    <i class="fas fa-user-shield"></i>
                    <span class="nav-text">System Administrators</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= ($menu ?? '') == 'data' ? 'active' : '' ?>" href="<?= base_url('dakoii/data') ?>">
                    <i class="fas fa-database"></i>
                    <span class="nav-text">Data Management</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= ($menu ?? '') == 'locations' ? 'active' : '' ?>" href="<?= base_url('dakoii/locations') ?>">
                    <i class="fas fa-map-marker-alt"></i>
                    <span class="nav-text">Locations</span>
                </a>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= ($menu ?? '') == 'system' ? 'active' : '' ?>" href="<?= base_url('dakoii/system') ?>">
                    <i class="fas fa-cogs"></i>
                    <span class="nav-text">System Settings</span>
                </a>
            </li>
            
            <li class="nav-item mt-4">
                <a class="nav-link text-warning" href="<?= base_url('dakoii/logout') ?>">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="nav-text">Logout</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Navigation -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <button class="sidebar-toggle d-md-none" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h4 class="mb-0 ms-3"><?= $title ?? 'Dakoii Admin Portal' ?></h4>
            </div>
            
            <div class="d-flex align-items-center">
                <?php if (session()->has('dakoii_logged_in')): ?>
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= session()->get('dakoii_name') ?>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?= base_url('dakoii/logout') ?>">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a></li>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Flash Messages -->
            <?php if (session()->has('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <?= session('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> <?= session('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->has('warning')): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> <?= session('warning') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Page Content -->
            <?= $this->renderSection('content') ?>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <p class="mb-0">&copy; 2024 <a href="https://www.dakoiims.com" class="text-white">Dakoii Systems</a>. 
            <?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></p>
        </footer>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Global CSRF token management
        window.csrfToken = '<?= csrf_token() ?>';
        window.csrfHash = '<?= csrf_hash() ?>';

        function updateCSRFToken(form) {
            const csrfInput = form.querySelector('input[name="' + window.csrfToken + '"]');
            if (csrfInput) {
                csrfInput.value = window.csrfHash;
            } else {
                // Create CSRF input if it doesn't exist
                const newInput = document.createElement('input');
                newInput.type = 'hidden';
                newInput.name = window.csrfToken;
                newInput.value = window.csrfHash;
                form.appendChild(newInput);
            }
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');

            sidebar.classList.toggle('show');
        }

        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Close mobile sidebar when clicking outside
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.querySelector('.sidebar-toggle');

            if (window.innerWidth <= 768) {
                if (!sidebar.contains(event.target) && !sidebarToggle.contains(event.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
    </script>
</body>
</html>
