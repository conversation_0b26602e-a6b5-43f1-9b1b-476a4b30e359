<?php namespace App\Controllers;

use App\Models\usersModel;

class Users extends BaseController
{
    protected $usersModel;
    protected $session;
    protected $permissionsModel;
    protected $districtModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->usersModel = new usersModel();
        $this->session = session();
        $this->permissionsModel = new \App\Models\PermissionsUserDistrictsModel();
        $this->districtModel = new \App\Models\districtModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Users Management',
            'menu' => 'users',
            'users' => $this->usersModel->where('org_id', $this->session->get('org_id'))->findAll()
        ];
        return view('admindash/users', $data);
    }

    public function add_user()
    {
        $validation = \Config\Services::validation();

        $rules = [
            'name' => 'required|min_length[3]',
            'password' => 'required|min_length[6]',
            'role' => 'required',
            'position' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->with('error', $validation->listErrors());
        }

        $data = [
            'org_id' => session('org_id'),
            'fileno' => $this->request->getPost('fileno'),
            'name' => $this->request->getPost('name'),
            'password' => password_hash($this->request->getPost('password'), PASSWORD_DEFAULT),
            'role' => $this->request->getPost('role'),
            'position' => $this->request->getPost('position'),
            'status' => 1,
            'created_by' => session('username')
        ];

        try {
            $this->usersModel->insert($data);
            return redirect()->back()->with('success', 'User added successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to add user');
        }
    }

    public function update_user()
    {
        $id = $this->request->getPost('id');
        $validation = \Config\Services::validation();

        $rules = [
            'name' => 'required|min_length[3]',
            'role' => 'required',
            'position' => 'required',
            'fileno' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->with('error', $validation->listErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'fileno' => $this->request->getPost('fileno'),
            'role' => $this->request->getPost('role'),
            'position' => $this->request->getPost('position'),
            'status' => $this->request->getPost('status'),
            'updated_by' => session('username')
        ];

        if ($this->request->getPost('password')) {
            $data['password'] = password_hash($this->request->getPost('password'), PASSWORD_DEFAULT);
        }

        try {
            $this->usersModel->update($id, $data);
            return redirect()->back()->with('success', 'User updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update user');
        }
    }

    public function delete_user($id)
    {
        try {
            $this->usersModel->delete($id);
            return redirect()->back()->with('success', 'User deleted successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to delete user');
        }
    }

    public function check_fileno()
    {
        if ($this->request->isAJAX()) {
            $fileno = $this->request->getPost('fileno');
            $exists = $this->usersModel->where('fileno', $fileno)
                                     ->where('org_id', session('org_id'))
                                     ->countAllResults() > 0;

            return $this->response->setJSON([
                'exists' => $exists,
                'message' => $exists ? 'This file number is already taken' : 'File number is available'
            ]);
        }
        return $this->response->setJSON(['error' => 'Invalid request']);
    }

    public function view($id)
    {
        $user = $this->usersModel->where('org_id', $this->session->get('org_id'))
                                ->find($id);

        if (!$user) {
            return redirect()->back()->with('error', 'User not found');
        }

        // Get user's district permissions using the model method
        $user_districts = $this->permissionsModel->getUserDistricts($id, session('org_id'));

        // Get all districts
        $all_districts = $this->districtModel->findAll();

        // Get assigned district IDs
        $assigned_district_ids = array_column($user_districts, 'district_id');

        // Filter out assigned districts from available districts
        $available_districts = array_filter($all_districts, function($district) use ($assigned_district_ids) {
            return !in_array($district['id'], $assigned_district_ids);
        });

        $data = [
            'title' => 'User Profile',
            'menu' => 'users',
            'user' => $user,
            'user_districts' => $user_districts,
            'available_districts' => $available_districts
        ];

        return view('admindash/users_view_profile', $data);
    }

    public function add_district_permission($user_id)
    {
        $district_id = $this->request->getPost('district_id');
        $default_district = $this->request->getPost('default_district') ? 1 : 0;

        // If setting as default, remove default from other districts
        if ($default_district) {
            $this->permissionsModel
                ->where('user_id', $user_id)
                ->where('org_id', session('org_id'))
                ->set(['default_district' => 0])
                ->update();
        }

        $data = [
            'org_id' => session('org_id'),
            'user_id' => $user_id,
            'district_id' => $district_id,
            'default_district' => $default_district,
            'created_by' => session('emp_id')
        ];

        try {
            $this->permissionsModel->insert($data);
            return redirect()->back()->with('success', 'District permission added successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to add district permission');
        }
    }

    public function set_default_district($permission_id)
    {
        $permission = $this->permissionsModel->find($permission_id);

        if (!$permission) {
            return redirect()->back()->with('error', 'Permission not found');
        }

        // Remove default from other districts
        $this->permissionsModel
            ->where('user_id', $permission['user_id'])
            ->where('org_id', session('org_id'))
            ->set(['default_district' => 0])
            ->update();

        // Set new default
        try {
            $this->permissionsModel->update($permission_id, [
                'default_district' => 1,
                'updated_by' => session('emp_id')
            ]);
            return redirect()->back()->with('success', 'Default district updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update default district');
        }
    }

    public function remove_district_permission($permission_id)
    {
        try {
            $this->permissionsModel->delete($permission_id);
            return redirect()->back()->with('success', 'District permission removed successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to remove district permission');
        }
    }
}