<?php

namespace App\Models;

use CodeIgniter\Model;

class DakoiiOrgModel extends Model
{
    protected $table      = 'dakoii_org';
    protected $primaryKey = 'id';

    protected $useAutoIncrement = true;

    protected $returnType     = 'array';
    protected $useSoftDeletes = false;

    protected $allowedFields = [
        'orgcode',
        'name',
        'description',
        'addlockprov',
        'addlockcountry',
        'orglogo',
        'is_locationlocked',
        'province_json',
        'district_json',
        'llg_json',
        'is_active',
        'license_status'
    ];

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = '';

    protected $validationRules    = [];
    protected $validationMessages = [];
    protected $skipValidation     = false;
}
