<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="description" content="Dakoii Admin Portal Login">
    <link rel="shortcut icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico" type="image/x-icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --dakoii-primary: #2c3e50;
            --dakoii-secondary: #34495e;
            --dakoii-accent: #3498db;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--dakoii-primary) 0%, var(--dakoii-secondary) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            margin: 2rem;
        }

        .login-header {
            background: linear-gradient(135deg, var(--dakoii-primary) 0%, var(--dakoii-accent) 100%);
            color: white;
            text-align: center;
            padding: 2rem;
        }

        .login-header .logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 1rem;
            border: 3px solid white;
        }

        .login-header h4 {
            margin: 0;
            font-weight: 600;
        }

        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .login-body {
            padding: 2rem;
        }

        .form-floating {
            margin-bottom: 1rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--dakoii-accent);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, var(--dakoii-accent) 0%, #2980b9 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }

        .btn-login:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: none;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            z-index: 10;
        }

        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1rem;
        }

        .back-link {
            text-align: center;
            margin-top: 1rem;
        }

        .back-link a {
            color: var(--dakoii-accent);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        .login-footer {
            background: #f8f9fa;
            text-align: center;
            padding: 1rem;
            color: #6c757d;
            font-size: 0.8rem;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>
    
    <title><?= $title ?></title>
</head>
<body>
    <div class="login-container">
        <!-- Login Header -->
        <div class="login-header">
            <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Dakoii Logo" class="logo">
            <h4>Dakoii Admin Portal</h4>
            <p>Agricultural Data Management System</p>
        </div>

        <!-- Login Body -->
        <div class="login-body">
            <!-- Flash Messages -->
            <?php if (session()->has('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> <?= session('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->has('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <?= session('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Login Form -->
            <?= form_open(base_url('dakoii/login'), ['id' => 'loginForm', 'method' => 'post']) ?>
                <?= csrf_field() ?>
                <div class="form-floating">
                    <input type="text" class="form-control" id="username" name="username"
                           placeholder="Enter your username" required maxlength="255"
                           value="<?= old('username') ?>">
                    <label for="username">
                        <i class="fas fa-user"></i> Username
                    </label>
                </div>

                <div class="form-floating position-relative">
                    <input type="password" class="form-control" id="password" name="password" 
                           placeholder="Enter your password" required>
                    <label for="password">
                        <i class="fas fa-lock"></i> Password
                    </label>
                    <button type="button" class="password-toggle" id="togglePassword">
                        <i class="fas fa-eye" id="toggleIcon"></i>
                    </button>
                </div>

                <button type="submit" class="btn btn-primary btn-login" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i> Sign In
                </button>
            <?= form_close() ?>

            <!-- Back Link -->
            <div class="back-link">
                <a href="<?= base_url() ?>">
                    <i class="fas fa-arrow-left"></i> Back to Main Site
                </a>
            </div>
        </div>

        <!-- Login Footer -->
        <div class="login-footer">
            <p class="mb-0">&copy; 2024 Dakoii Systems. All rights reserved.</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Password toggle functionality
            const togglePassword = document.getElementById('togglePassword');
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            togglePassword.addEventListener('click', function() {
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    toggleIcon.classList.remove('fa-eye');
                    toggleIcon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    toggleIcon.classList.remove('fa-eye-slash');
                    toggleIcon.classList.add('fa-eye');
                }
            });

            // Form submission with loading state
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');

            loginForm.addEventListener('submit', function() {
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Signing in...';

                // Re-enable button after 10 seconds as fallback
                setTimeout(function() {
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Sign In';
                }, 10000);
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);

            // Focus on username field when page loads
            document.getElementById('username').focus();

            // Add enter key support for password field
            passwordField.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    loginForm.submit();
                }
            });
        });
    </script>
</body>
</html>
