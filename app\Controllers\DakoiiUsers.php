<?php

namespace App\Controllers;

use App\Models\DakoiiUsersModel;
use App\Models\DakoiiOrgModel;

class DakoiiUsers extends BaseController
{
    public $session;
    public $dusersModel;
    public $orgModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->dusersModel = new DakoiiUsersModel();
        $this->orgModel = new DakoiiOrgModel();
    }

    /**
     * Display users list
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "User Management";
        $data['menu'] = "users";
        $data['users'] = $this->dusersModel->findAll();
        $data['organizations'] = $this->orgModel->findAll();

        return view('dakoii/dakoii_users_index', $data);
    }

    /**
     * Show create user form
     */
    public function create()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Create User";
        $data['menu'] = "users";
        $data['organizations'] = $this->orgModel->where('is_active', 1)->findAll();

        return view('dakoii/dakoii_users_create', $data);
    }

    /**
     * Store new user
     */
    public function store()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/users');
        }

        // Validate input
        $rules = [
            'name' => 'required|min_length[3]|max_length[255]',
            'username' => 'required|min_length[3]|max_length[255]|is_unique[dakoii_users.username]',
            'password' => 'required|min_length[6]',
            'role' => 'required|in_list[user,moderator,admin]',
            'orgcode' => 'required'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter valid user data. Username must be unique.');
            return redirect()->to('dakoii/users/create')->withInput();
        }

        $data = [
            'name' => trim($this->request->getPost('name')),
            'username' => trim($this->request->getPost('username')),
            'password' => $this->request->getPost('password'), // Will be hashed by model
            'role' => $this->request->getPost('role'),
            'orgcode' => $this->request->getPost('orgcode'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0
        ];

        if ($this->dusersModel->insert($data)) {
            session()->setFlashdata('success', 'User "' . $data['name'] . '" created successfully!');
            return redirect()->to('dakoii/users');
        } else {
            session()->setFlashdata('error', 'Failed to create user. Please try again.');
            return redirect()->to('dakoii/users/create')->withInput();
        }
    }

    /**
     * Show edit user form
     */
    public function edit($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $user = $this->dusersModel->find($id);
        if (!$user) {
            session()->setFlashdata('error', 'User not found');
            return redirect()->to('dakoii/users');
        }

        $data['title'] = "Edit User - " . $user['name'];
        $data['menu'] = "users";
        $data['user'] = $user;
        $data['organizations'] = $this->orgModel->where('is_active', 1)->findAll();

        return view('dakoii/dakoii_users_edit', $data);
    }

    /**
     * Update user
     */
    public function update($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/users');
        }

        $user = $this->dusersModel->find($id);
        if (!$user) {
            session()->setFlashdata('error', 'User not found');
            return redirect()->to('dakoii/users');
        }

        // Validate input
        $rules = [
            'name' => 'required|min_length[3]|max_length[255]',
            'username' => 'required|min_length[3]|max_length[255]|is_unique[dakoii_users.username,id,' . $id . ']',
            'role' => 'required|in_list[user,moderator,admin]',
            'orgcode' => 'required'
        ];

        // Add password validation only if password is provided
        if (!empty($this->request->getPost('password'))) {
            $rules['password'] = 'min_length[6]';
        }

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter valid user data. Username must be unique.');
            return redirect()->to('dakoii/users/edit/' . $id)->withInput();
        }

        $data = [
            'name' => trim($this->request->getPost('name')),
            'username' => trim($this->request->getPost('username')),
            'role' => $this->request->getPost('role'),
            'orgcode' => $this->request->getPost('orgcode'),
            'is_active' => $this->request->getPost('is_active') ? 1 : 0
        ];

        // Add password to update data only if provided
        if (!empty($this->request->getPost('password'))) {
            $data['password'] = $this->request->getPost('password'); // Will be hashed by model
        }

        if ($this->dusersModel->update($id, $data)) {
            session()->setFlashdata('success', 'User "' . $data['name'] . '" updated successfully!');
            return redirect()->to('dakoii/users');
        } else {
            session()->setFlashdata('error', 'Failed to update user. Please try again.');
            return redirect()->to('dakoii/users/edit/' . $id)->withInput();
        }
    }

    /**
     * Delete user (soft delete by setting is_active to 0)
     */
    public function delete($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/users');
        }

        $user = $this->dusersModel->find($id);
        if (!$user) {
            session()->setFlashdata('error', 'User not found');
            return redirect()->to('dakoii/users');
        }

        // Prevent deleting the current user
        if ($this->session->get('dakoii_user_id') == $id) {
            session()->setFlashdata('error', 'You cannot delete your own account');
            return redirect()->to('dakoii/users');
        }

        // Soft delete by setting is_active to 0
        $data = ['is_active' => 0];

        if ($this->dusersModel->update($id, $data)) {
            session()->setFlashdata('success', 'User "' . $user['name'] . '" has been deactivated successfully!');
        } else {
            session()->setFlashdata('error', 'Failed to deactivate user. Please try again.');
        }

        return redirect()->to('dakoii/users');
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }

    /**
     * Get current user information
     */
    private function getCurrentUser()
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return [
            'id' => $this->session->get('dakoii_user_id'),
            'name' => $this->session->get('dakoii_name'),
            'username' => $this->session->get('dakoii_username'),
            'role' => $this->session->get('dakoii_role'),
            'orgcode' => $this->session->get('dakoii_orgcode')
        ];
    }
}
