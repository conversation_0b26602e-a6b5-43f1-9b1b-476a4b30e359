<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class DakoiiUserSeeder extends Seeder
{
    public function run()
    {
        // Check if admin user already exists
        $existingAdmin = $this->db->table('dakoii_users')->where('username', 'admin')->get()->getRow();

        if ($existingAdmin) {
            echo "Admin user already exists. Updating password...\n";
            $this->db->table('dakoii_users')
                ->where('username', 'admin')
                ->update([
                    'password' => password_hash('admin123', PASSWORD_DEFAULT),
                    'is_active' => 1,
                    'role' => 'super_admin',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            echo "Admin password updated to: admin123\n";
            return;
        }

        // Create default Dakoii admin user
        $data = [
            'name' => 'Dakoii Administrator',
            'username' => 'admin',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'orgcode' => '',
            'role' => 'super_admin',
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $this->db->table('dakoii_users')->insert($data);

        echo "Default Dakoii admin user created:\n";
        echo "Username: admin\n";
        echo "Password: admin123\n";
        echo "Please change the password after first login.\n";
    }
}
