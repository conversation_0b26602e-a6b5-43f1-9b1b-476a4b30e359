<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organizations') ?>">Organizations</a></li>
        <li class="breadcrumb-item active">Create Organization</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Create New Organization</h2>
        <p class="text-muted mb-0">Add a new organization to the system</p>
    </div>
    <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> Back to Organizations
    </a>
</div>

<!-- Create Form -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus"></i> Organization Information
                </h5>
            </div>
            <div class="card-body">
                <form action="<?= base_url('dakoii/organizations/store') ?>" method="post" enctype="multipart/form-data">
                
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Basic Information</h6>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Organization Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name"
                                   value="<?= old('name') ?>">
                            <div class="form-text">Enter the full name of the organization</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="4" maxlength="1000"><?= old('description') ?></textarea>
                            <div class="form-text">Brief description of the organization (optional)</div>
                        </div>

                        <div class="mb-3">
                            <label for="org_logo" class="form-label">Organization Logo</label>
                            <input type="file" class="form-control" id="org_logo" name="org_logo" 
                                   accept="image/*">
                            <div class="form-text">Upload organization logo (JPG, PNG, GIF - Max 2MB)</div>
                        </div>
                    </div>

                    <!-- Location Settings -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Location Settings</h6>
                        
                        <div class="mb-3">
                            <label for="country" class="form-label">Address Lock Country</label>
                            <select class="form-select" id="country" name="country">
                                <option value="">Select Country (Optional)</option>
                                <?php foreach ($countries as $country): ?>
                                    <option value="<?= $country['id'] ?>" <?= old('country') == $country['id'] ? 'selected' : '' ?>>
                                        <?= esc($country['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Restrict organization to specific country</div>
                        </div>

                        <div class="mb-3">
                            <label for="province" class="form-label">Address Lock Province</label>
                            <select class="form-select" id="province" name="province">
                                <option value="">Select Province (Optional)</option>
                                <?php foreach ($provinces as $province): ?>
                                    <option value="<?= $province['id'] ?>" <?= old('province') == $province['id'] ? 'selected' : '' ?>>
                                        <?= esc($province['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Restrict organization to specific province</div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Location Lock:</strong> If you select a country or province, the organization will be restricted to operate only in those locations.
                        </div>

                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <strong>Default Settings:</strong>
                            <ul class="mb-0 mt-2">
                                <li>Status: Active</li>
                                <li>License: Trial (30 days)</li>
                                <li>Organization Code: Auto-generated</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr>
                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Organization
                            </button>
                        </div>
                    </div>
                </div>

                </form>
            </div>
        </div>
    </div>
</div>

<style>
.form-label {
    font-weight: 600;
}

.text-danger {
    color: #dc3545 !important;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.alert {
    border-radius: 10px;
}

.card {
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on name field
    document.getElementById('name').focus();
});
</script>

<?= $this->endSection() ?>
