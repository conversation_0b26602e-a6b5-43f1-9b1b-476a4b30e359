<?php

namespace App\Controllers;

use App\Models\AdxCountryModel;
use App\Models\DakoiiUsersModel;
use App\Models\DakoiiOrgModel;
use App\Models\AdxProvinceModel;
use App\Models\UsersModel;

class DakoiiOrganizations extends BaseController
{
    public $session;
    public $dusersModel;
    public $usersModel;
    public $orgModel;
    public $countryModel;
    public $provinceModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        // Initialize models
        $this->dusersModel = new DakoiiUsersModel();
        $this->usersModel = new UsersModel();
        $this->orgModel = new DakoiiOrgModel();
        $this->countryModel = new AdxCountryModel();
        $this->provinceModel = new AdxProvinceModel();
    }

    /**
     * Display organizations list
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Organizations Management";
        $data['menu'] = "organizations";
        $data['organizations'] = $this->orgModel->orderBy('id', 'DESC')->findAll();

        return view('dakoii/dakoii_organizations_index', $data);
    }

    /**
     * Show create organization form
     */
    public function create()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Create Organization";
        $data['menu'] = "organizations";
        $data['countries'] = $this->countryModel->findAll();
        $data['provinces'] = $this->provinceModel->findAll();

        return view('dakoii/dakoii_organizations_create', $data);
    }

    /**
     * Store new organization
     */
    public function store()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/organizations');
        }

        // Validate input
        $rules = [
            'name' => 'required|min_length[3]|max_length[255]',
            'description' => 'permit_empty|max_length[1000]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter valid organization data');
            return redirect()->to('dakoii/organizations/create')->withInput();
        }

        // Generate unique organization code
        $orgcode = rand(11111, 99999);
        while (!empty($this->orgModel->where('orgcode', $orgcode)->first())) {
            $orgcode = rand(11111, 99999);
        }

        $data = [
            'orgcode' => $orgcode,
            'name' => trim($this->request->getPost('name')),
            'description' => trim($this->request->getPost('description')),
            'addlockcountry' => $this->request->getPost('country'),
            'addlockprov' => $this->request->getPost('province'),
            'is_locationlocked' => $this->request->getPost('country') ? 1 : 0,
            'is_active' => 1,
            'license_status' => 'trial'
        ];

        // Handle logo upload
        $logoFile = $this->request->getFile('org_logo');
        if ($logoFile && $logoFile->isValid() && $logoFile->getSize() > 0) {
            $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();
            
            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            $logoFile->move($uploadPath, $newName);
            $data['orglogo'] = base_url() . 'public/uploads/org_logo/' . $newName;
        }

        if ($insertId = $this->orgModel->insert($data)) {
            session()->setFlashdata('success', 'Organization "' . $data['name'] . '" created successfully!');
            return redirect()->to('dakoii/organizations/show/' . $insertId);
        } else {
            session()->setFlashdata('error', 'Failed to create organization. Please try again.');
            return redirect()->to('dakoii/organizations/create')->withInput();
        }
    }

    /**
     * Show organization details
     */
    public function show($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $org = $this->orgModel->find($id);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        $data['title'] = "Organization Details - " . $org['name'];
        $data['menu'] = "organizations";
        $data['org'] = $org;

        // Get organization system administrators from main users table
        // Note: Join using org_id relationship between dakoii_org.id and users.org_id
        $usersModel = new \App\Models\UsersModel();
        $data['admins'] = $usersModel->where('org_id', $org['id'])
                                    ->where('role', 'admin')
                                    ->findAll();

        // Get country and province names
        if (!empty($org['addlockcountry'])) {
            $country = $this->countryModel->find($org['addlockcountry']);
            $data['country_name'] = $country ? $country['name'] : 'Unknown';
        }

        if (!empty($org['addlockprov'])) {
            $province = $this->provinceModel->find($org['addlockprov']);
            $data['province_name'] = $province ? $province['name'] : 'Unknown';
        }

        // Get all countries and provinces for edit form
        $data['countries'] = $this->countryModel->findAll();
        $data['provinces'] = $this->provinceModel->findAll();

        return view('dakoii/dakoii_organizations_show', $data);
    }

    /**
     * Show edit organization form
     */
    public function edit($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $org = $this->orgModel->find($id);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        $data['title'] = "Edit Organization - " . $org['name'];
        $data['menu'] = "organizations";
        $data['org'] = $org;
        $data['countries'] = $this->countryModel->findAll();
        $data['provinces'] = $this->provinceModel->findAll();

        return view('dakoii/dakoii_organizations_edit', $data);
    }

    /**
     * Update organization
     */
    public function update($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/organizations');
        }

        $org = $this->orgModel->find($id);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        // Validate input
        $rules = [
            'name' => 'required|min_length[3]|max_length[255]',
            'description' => 'permit_empty|max_length[1000]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter valid organization data');
            return redirect()->to('dakoii/organizations/edit/' . $id)->withInput();
        }

        $data = [
            'name' => trim($this->request->getPost('name')),
            'description' => trim($this->request->getPost('description')),
            'addlockcountry' => $this->request->getPost('country'),
            'addlockprov' => $this->request->getPost('province'),
            'is_locationlocked' => $this->request->getPost('country') ? 1 : 0,
            'is_active' => $this->request->getPost('status')
        ];

        // Handle logo upload
        $logoFile = $this->request->getFile('org_logo');
        if ($logoFile && $logoFile->isValid() && $logoFile->getSize() > 0) {
            // Delete old logo if exists
            if (!empty($org['orglogo'])) {
                $oldLogoPath = str_replace(base_url(), ROOTPATH . 'public/', $org['orglogo']);
                if (file_exists($oldLogoPath)) {
                    unlink($oldLogoPath);
                }
            }

            $newName = $org['orgcode'] . "_" . time() . '.' . $logoFile->getExtension();
            
            // Create upload directory if it doesn't exist
            $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }
            
            $logoFile->move($uploadPath, $newName);
            $data['orglogo'] = base_url() . 'public/uploads/org_logo/' . $newName;
        }

        if ($this->orgModel->update($org['id'], $data)) {
            session()->setFlashdata('success', 'Organization "' . $data['name'] . '" updated successfully!');
            return redirect()->to('dakoii/organizations/show/' . $id);
        } else {
            session()->setFlashdata('error', 'Failed to update organization. Please try again.');
            return redirect()->to('dakoii/organizations/edit/' . $id)->withInput();
        }
    }

    /**
     * Update organization license status
     */
    public function updateLicense($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/organizations');
        }

        $org = $this->orgModel->find($id);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        $licenseStatus = $this->request->getPost('license_status');
        if (!in_array($licenseStatus, ['paid', 'trial'])) {
            session()->setFlashdata('error', 'Invalid license status');
            return redirect()->to('dakoii/organizations/show/' . $id);
        }

        $data = ['license_status' => $licenseStatus];

        if ($this->orgModel->update($org['id'], $data)) {
            session()->setFlashdata('success', 'License status updated successfully!');
        } else {
            session()->setFlashdata('error', 'Failed to update license status');
        }

        return redirect()->to('dakoii/organizations/show/' . $id);
    }

    /**
     * Delete organization
     */
    public function delete($id)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        // Only process POST requests
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to('dakoii/organizations');
        }

        $org = $this->orgModel->find($id);
        if (!$org) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organizations');
        }

        // Check if organization has any users before deleting
        $usersCount = $this->usersModel->where('org_id', $id)->countAllResults();
        if ($usersCount > 0) {
            session()->setFlashdata('error', 'Cannot delete organization. It has ' . $usersCount . ' user(s) associated with it.');
            return redirect()->to('dakoii/organizations/show/' . $id);
        }

        // Delete organization logo if exists
        if (!empty($org['orglogo'])) {
            $logoPath = str_replace(base_url(), ROOTPATH . 'public/', $org['orglogo']);
            if (file_exists($logoPath)) {
                unlink($logoPath);
            }
        }

        if ($this->orgModel->delete($id)) {
            session()->setFlashdata('success', 'Organization "' . $org['name'] . '" deleted successfully!');
        } else {
            session()->setFlashdata('error', 'Failed to delete organization. Please try again.');
        }

        return redirect()->to('dakoii/organizations');
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }
}
