<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organizations') ?>">Organizations</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organizations/show/' . $org['id']) ?>"><?= esc($org['name']) ?></a></li>
        <li class="breadcrumb-item active">Edit</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Edit Organization</h2>
        <p class="text-muted mb-0">Update organization information</p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/organizations/show/' . $org['id']) ?>" class="btn btn-outline-info">
            <i class="fas fa-eye"></i> View Details
        </a>
        <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
    </div>
</div>

<!-- Edit Form -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit"></i> Update Organization Information
                </h5>
            </div>
            <div class="card-body">
                <form action="<?= base_url('dakoii/organizations/update/' . $org['id']) ?>" method="post" enctype="multipart/form-data">
                
                <div class="row">
                    <!-- Basic Information -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Basic Information</h6>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Organization Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?= old('name', $org['name']) ?>" required maxlength="255">
                            <div class="form-text">Enter the full name of the organization</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="4" maxlength="1000"><?= old('description', $org['description']) ?></textarea>
                            <div class="form-text">Brief description of the organization</div>
                        </div>

                        <div class="mb-3">
                            <label for="org_logo" class="form-label">Organization Logo</label>
                            <?php if (!empty($org['orglogo'])): ?>
                                <div class="mb-2">
                                    <img src="<?= imgcheck($org['orglogo']) ?>" alt="Current Logo" 
                                         class="img-thumbnail" style="max-height: 100px;">
                                    <div class="form-text">Current logo</div>
                                </div>
                            <?php endif; ?>
                            <input type="file" class="form-control" id="org_logo" name="org_logo" 
                                   accept="image/*">
                            <div class="form-text">Upload new logo to replace current one (JPG, PNG, GIF - Max 2MB)</div>
                        </div>
                    </div>

                    <!-- Location Settings -->
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Location Settings</h6>
                        
                        <div class="mb-3">
                            <label for="country" class="form-label">Address Lock Country</label>
                            <select class="form-select" id="country" name="country">
                                <option value="">Select Country (Optional)</option>
                                <?php foreach ($countries as $country): ?>
                                    <option value="<?= $country['id'] ?>" 
                                            <?= old('country', $org['addlockcountry']) == $country['id'] ? 'selected' : '' ?>>
                                        <?= esc($country['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Restrict organization to specific country</div>
                        </div>

                        <div class="mb-3">
                            <label for="province" class="form-label">Address Lock Province</label>
                            <select class="form-select" id="province" name="province">
                                <option value="">Select Province (Optional)</option>
                                <?php foreach ($provinces as $province): ?>
                                    <option value="<?= $province['id'] ?>"
                                            <?= old('province', $org['addlockprov']) == $province['id'] ? 'selected' : '' ?>>
                                        <?= esc($province['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Restrict organization to specific province</div>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Organization Status</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="1" <?= old('status', $org['is_active']) == '1' ? 'selected' : '' ?>>Active</option>
                                <option value="0" <?= old('status', $org['is_active']) == '0' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                            <div class="form-text">Set organization status</div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Organization Code:</strong> <?= esc($org['orgcode']) ?>
                            <div class="form-text">Organization code cannot be changed</div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Current Status:</strong>
                            <ul class="mb-0 mt-2">
                                <li>Status: <?= $org['is_active'] ? 'Active' : 'Inactive' ?></li>
                                <li>License: <?= ucfirst($org['license_status']) ?></li>
                                <li>Location Lock: <?= $org['is_locationlocked'] ? 'Enabled' : 'Disabled' ?></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <hr>
                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('dakoii/organizations/show/' . $org['id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Organization
                            </button>
                        </div>
                    </div>
                </div>

                </form>
            </div>
        </div>
    </div>
</div>

<style>
.form-label {
    font-weight: 600;
}

.text-danger {
    color: #dc3545 !important;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.alert {
    border-radius: 10px;
}

.card {
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.img-thumbnail {
    border-radius: 10px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // File upload preview
    const logoInput = document.getElementById('org_logo');
    
    logoInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validate file size (2MB)
            if (file.size > 2 * 1024 * 1024) {
                alert('File size must be less than 2MB');
                this.value = '';
                return;
            }
            
            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a valid image file (JPG, PNG, GIF)');
                this.value = '';
                return;
            }
        }
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        
        if (name.length < 3) {
            e.preventDefault();
            alert('Organization name must be at least 3 characters long');
            document.getElementById('name').focus();
            return;
        }
    });
});
</script>

<?= $this->endSection() ?>
