<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\DakoiiUsersModel;

class ListDakoiiUsers extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'dakoii:users';
    protected $description = 'Lists all Dakoii users in the database';

    public function run(array $params)
    {
        $model = new DakoiiUsersModel();
        $users = $model->findAll();

        if (empty($users)) {
            CLI::write('No Dakoii users found in the database.', 'yellow');
            CLI::write('');
            CLI::write('To create a default admin user, run:');
            CLI::write('php spark db:seed DakoiiUserSeeder', 'green');
            return;
        }

        CLI::write('Dakoii Users:', 'green');
        CLI::write(str_repeat('-', 80));
        CLI::write(sprintf('%-5s %-20s %-20s %-15s %-10s', 'ID', 'Name', 'Username', 'Role', 'Active'));
        CLI::write(str_repeat('-', 80));

        foreach ($users as $user) {
            CLI::write(sprintf(
                '%-5s %-20s %-20s %-15s %-10s',
                $user['id'],
                substr($user['name'], 0, 19),
                substr($user['username'], 0, 19),
                $user['role'],
                $user['is_active'] ? 'Yes' : 'No'
            ));
        }

        CLI::write(str_repeat('-', 80));
        CLI::write('Total users: ' . count($users));
    }
}
