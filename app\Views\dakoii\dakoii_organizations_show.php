<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organizations') ?>">Organizations</a></li>
        <li class="breadcrumb-item active"><?= esc($org['name']) ?></li>
    </ol>
</nav>

<!-- Organization Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0"><?= esc($org['name']) ?></h2>
        <p class="text-muted mb-0">Organization Code: <code><?= esc($org['orgcode']) ?></code></p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to List
        </a>
        <a href="<?= base_url('dakoii/organizations/edit/' . $org['id']) ?>" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit Organization
        </a>
    </div>
</div>

<div class="row">
    <!-- Organization Details -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Organization Details
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Logo Section -->
                    <div class="col-md-4 text-center mb-4">
                        <?php if (!empty($org['orglogo'])): ?>
                            <img src="<?= imgcheck($org['orglogo']) ?>" alt="Organization Logo" 
                                 class="img-fluid rounded shadow" style="max-height: 200px;">
                        <?php else: ?>
                            <div class="bg-light rounded d-flex align-items-center justify-content-center shadow" 
                                 style="height: 200px;">
                                <i class="fas fa-building fa-4x text-muted"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Details Section -->
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-medium text-muted" style="width: 40%;">Organization Name:</td>
                                <td><?= esc($org['name']) ?></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">Organization Code:</td>
                                <td><code class="bg-light px-2 py-1 rounded"><?= esc($org['orgcode']) ?></code></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">Description:</td>
                                <td><?= nl2br(esc($org['description'])) ?: '<em class="text-muted">No description provided</em>' ?></td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">Status:</td>
                                <td>
                                    <span class="badge bg-<?= $org['is_active'] ? 'success' : 'danger' ?> fs-6">
                                        <i class="fas fa-<?= $org['is_active'] ? 'check' : 'times' ?>"></i>
                                        <?= $org['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">License Status:</td>
                                <td>
                                    <span class="badge bg-<?= $org['license_status'] == 'paid' ? 'success' : 'warning' ?> fs-6">
                                        <i class="fas fa-<?= $org['license_status'] == 'paid' ? 'crown' : 'clock' ?>"></i>
                                        <?= ucfirst($org['license_status']) ?>
                                    </span>
                                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" 
                                            data-bs-toggle="modal" data-bs-target="#licenseModal">
                                        <i class="fas fa-key"></i> Update License
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-medium text-muted">Location Lock:</td>
                                <td>
                                    <?php if ($org['is_locationlocked']): ?>
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-lock"></i> Locked
                                        </span>
                                        <div class="mt-1">
                                            <small class="text-muted">
                                                <?php if (isset($country_name)): ?>
                                                    Country: <?= esc($country_name) ?><br>
                                                <?php endif; ?>
                                                <?php if (isset($province_name)): ?>
                                                    Province: <?= esc($province_name) ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php else: ?>
                                        <span class="badge bg-success">
                                            <i class="fas fa-unlock"></i> Unlocked
                                        </span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Administrators -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user-shield"></i> System Administrators
                </h5>
                <div class="btn-group">
                    <a href="<?= base_url('dakoii/system-admins') ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-list"></i> View All
                    </a>
                    <a href="<?= base_url('dakoii/system-admins/create') ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Add Admin
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($admins)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($admins as $admin): ?>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?= esc($admin['name']) ?></h6>
                                    <p class="mb-1 text-muted">@<?= esc($admin['username']) ?></p>
                                    <div>
                                        <span class="badge bg-info me-1"><?= ucfirst(esc($admin['role'])) ?></span>
                                        <span class="badge bg-<?= $admin['is_active'] ? 'success' : 'danger' ?>">
                                            <?= $admin['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                            data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="<?= base_url('dakoii/system-admins/show/' . $admin['id']) ?>">
                                                <i class="fas fa-eye"></i> View Details
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="<?= base_url('dakoii/system-admins/edit/' . $admin['id']) ?>">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No administrators found</p>
                        <a href="<?= base_url('dakoii/system-admins/create') ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add First Admin
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- License Status Modal -->
<div class="modal fade" id="licenseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key"></i> Update License Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <?= form_open('dakoii/organizations/update-license/' . $org['id']) ?>
            <?= csrf_field() ?>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">License Status</label>
                    <select class="form-select" name="license_status" required>
                        <option value="paid" <?= $org['license_status'] == 'paid' ? 'selected' : '' ?>>
                            <i class="fas fa-crown"></i> Paid License
                        </option>
                        <option value="trial" <?= $org['license_status'] == 'trial' ? 'selected' : '' ?>>
                            <i class="fas fa-clock"></i> Trial License
                        </option>
                    </select>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Note:</strong> Changing the license status will affect the organization's access to premium features.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update License
                </button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>



<?= $this->endSection() ?>
