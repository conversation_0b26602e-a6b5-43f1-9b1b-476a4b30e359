<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Dashboard Overview Cards -->
<div class="row mb-4">
    <!-- Organizations Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Organizations
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['organizations']['total'] ?>
                        </div>
                        <div class="text-xs text-muted">
                            Active: <?= $stats['organizations']['active'] ?> | 
                            Paid: <?= $stats['organizations']['paid'] ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-building fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            System Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['users']['total'] ?>
                        </div>
                        <div class="text-xs text-muted">
                            Active: <?= $stats['users']['active'] ?> | 
                            Admins: <?= $stats['users']['admins'] ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Management Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Data Items
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= array_sum($stats['data']) ?>
                        </div>
                        <div class="text-xs text-muted">
                            Crops: <?= $stats['data']['crops'] ?> | 
                            Livestock: <?= $stats['data']['livestock'] ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-database fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Locations Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Locations
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= array_sum($stats['locations']) ?>
                        </div>
                        <div class="text-xs text-muted">
                            Provinces: <?= $stats['locations']['provinces'] ?> | 
                            Districts: <?= $stats['locations']['districts'] ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('dakoii/organizations/create') ?>" class="btn btn-primary btn-block">
                            <i class="fas fa-plus"></i> Add Organization
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('dakoii/users/create') ?>" class="btn btn-success btn-block">
                            <i class="fas fa-user-plus"></i> Add User
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('dakoii/data') ?>" class="btn btn-info btn-block">
                            <i class="fas fa-database"></i> Manage Data
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= base_url('dakoii/locations') ?>" class="btn btn-warning btn-block">
                            <i class="fas fa-map"></i> View Locations
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Organizations and Data Statistics -->
<div class="row">
    <!-- Recent Organizations -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-building"></i> Recent Organizations
                </h5>
                <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-sm btn-primary">
                    View All
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Name</th>
                                <th>Code</th>
                                <th>License</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($org, 0, 5) as $organization): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if ($organization['orglogo']): ?>
                                            <img src="<?= imgcheck($organization['orglogo']) ?>" alt="Logo" 
                                                 class="rounded me-2" style="height: 30px; width: 30px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-secondary rounded me-2 d-flex align-items-center justify-content-center" 
                                                 style="height: 30px; width: 30px;">
                                                <i class="fas fa-building text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                        <span class="fw-medium"><?= esc($organization['name']) ?></span>
                                    </div>
                                </td>
                                <td>
                                    <code><?= esc($organization['orgcode']) ?></code>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $organization['license_status'] == 'paid' ? 'success' : 'warning' ?>">
                                        <?= ucfirst($organization['license_status']) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $organization['is_active'] ? 'success' : 'danger' ?>">
                                        <?= $organization['is_active'] ? 'Active' : 'Inactive' ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="<?= base_url('dakoii/organizations/show/' . $organization['id']) ?>"
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Management Statistics -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> Data Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="text-sm">Crops</span>
                        <span class="badge bg-primary"><?= $stats['data']['crops'] ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-primary" style="width: <?= $stats['data']['crops'] > 0 ? ($stats['data']['crops'] / max(array_values($stats['data'])) * 100) : 0 ?>%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="text-sm">Fertilizers</span>
                        <span class="badge bg-success"><?= $stats['data']['fertilizers'] ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" style="width: <?= $stats['data']['fertilizers'] > 0 ? ($stats['data']['fertilizers'] / max(array_values($stats['data'])) * 100) : 0 ?>%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="text-sm">Pesticides</span>
                        <span class="badge bg-warning"><?= $stats['data']['pesticides'] ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-warning" style="width: <?= $stats['data']['pesticides'] > 0 ? ($stats['data']['pesticides'] / max(array_values($stats['data'])) * 100) : 0 ?>%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="text-sm">Livestock</span>
                        <span class="badge bg-info"><?= $stats['data']['livestock'] ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-info" style="width: <?= $stats['data']['livestock'] > 0 ? ($stats['data']['livestock'] / max(array_values($stats['data'])) * 100) : 0 ?>%"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="text-sm">Infections</span>
                        <span class="badge bg-danger"><?= $stats['data']['infections'] ?></span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-danger" style="width: <?= $stats['data']['infections'] > 0 ? ($stats['data']['infections'] / max(array_values($stats['data'])) * 100) : 0 ?>%"></div>
                    </div>
                </div>

                <div class="text-center mt-3">
                    <a href="<?= base_url('dakoii/data') ?>" class="btn btn-sm btn-outline-primary">
                        Manage Data <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Province Statistics -->
<?php if (!empty($province_stats)): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-map"></i> Location Statistics by Province
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Province</th>
                                <th>Districts</th>
                                <th>LLGs</th>
                                <th>Wards</th>
                                <th>Total Locations</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($province_stats as $stat): ?>
                            <tr>
                                <td class="fw-medium"><?= esc($stat['province']['name']) ?></td>
                                <td>
                                    <span class="badge bg-primary"><?= $stat['districts_count'] ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-success"><?= $stat['llgs_count'] ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= $stat['wards_count'] ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-dark">
                                        <?= $stat['districts_count'] + $stat['llgs_count'] + $stat['wards_count'] ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
.border-left-primary {
    border-left: 4px solid #4e73df !important;
}

.border-left-success {
    border-left: 4px solid #1cc88a !important;
}

.border-left-info {
    border-left: 4px solid #36b9cc !important;
}

.border-left-warning {
    border-left: 4px solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.progress {
    background-color: #f8f9fc;
}
</style>

<?= $this->endSection() ?>
